import React from 'react';
import { View, Text as RNText } from 'react-native';
import { Svg, Rect, Text } from 'react-native-svg';

/**
 * CONFIGURACIÓN RÁPIDA:
 * Para ajustar el espacio entre las barras y las etiquetas del eje X,
 * modifica la variable LABEL_SPACING_FROM_BARS dentro del componente.
 * Valores recomendados: 8-25 (12 es el valor actual)
 */

interface Dataset {
  data: number[];
  color?: () => string;
  strokeWidth?: number;
}

interface GroupedBarChartData {
  labels: string[];
  datasets: Dataset[];
  legend: string[];
}

interface GroupedBarChartProps {
  data: GroupedBarChartData;
  width: number;
  height: number;
  chartConfig: {
    backgroundColor: string;
    backgroundGradientFrom: string;
    backgroundGradientTo: string;
    decimalPlaces: number;
    color: (opacity?: number) => string;
    labelColor: (opacity?: number) => string;
  };
  style?: any;
  horizontalLabelRotation?: number;
  showValuesOnTopOfBars?: boolean;
}

const GroupedBarChart: React.FC<GroupedBarChartProps> = ({ data, width, height, chartConfig, style, horizontalLabelRotation }) => {
  const padding = 20;

  // Función para calcular la longitud máxima de las etiquetas
  const getMaxLabelLength = () => {
    if (!data.labels || data.labels.length === 0) return 0;
    return Math.max(...data.labels.map(label => label.length));
  };

  // Calcular el espacio necesario para las etiquetas basado en su longitud y rotación
  const maxLabelLength = getMaxLabelLength();
  const rotation = horizontalLabelRotation || 0;

  // *** VARIABLE DE CONFIGURACIÓN FÁCIL ACCESO ***
  // Ajusta este valor para controlar el espacio entre barras y etiquetas (8-25 recomendado)
  const LABEL_SPACING_FROM_BARS = 12;

  // Aumentar el espacio para etiquetas largas, especialmente cuando están rotadas
  const baseAxisHeight = LABEL_SPACING_FROM_BARS;
  const extraHeightForLongLabels = Math.max(0, (maxLabelLength - 10) * 1.5); // Reducido de 2 a 1.5
  const rotationFactor = Math.abs(rotation) > 0 ? 1.2 : 1; // Reducido de 1.5 a 1.2
  const axisHeight = baseAxisHeight + (extraHeightForLongLabels * rotationFactor);

  const legendHeight = 20; // extra space for legend
  const topPadding = 20; // extra padding at the top for labels
  const bottomPadding = padding + axisHeight;
  const chartHeight = height - topPadding - bottomPadding - legendHeight; // drawing area for bars

  const isDark = chartConfig.backgroundColor && chartConfig.backgroundColor.toLowerCase() !== '#ffffff';
  const labelBackgroundFill = isDark ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.8)';

  // Get max value among all datasets
  const allData = data.datasets.flatMap(ds => ds.data);
  const maxValue = Math.max(...allData, 0);

  // Determine bar width: for each label, we have data.datasets.length bars
  // Ajustado para dispositivos móviles - barras más compactas
  const groupCount = data.labels.length;
  const barsPerGroup = data.datasets.length;
  const numberOfBars = groupCount * barsPerGroup;
  const groupSpacing = 6; // Reducido de 10 a 6 para móviles
  const barSpacing = 3; // Reducido de 5 a 3 para móviles
  const totalBarSpacing = (groupCount - 1) * groupSpacing + numberOfBars * barSpacing;
  const barWidth = (width - 2 * padding - totalBarSpacing) / numberOfBars;

  // Function to format number with 2 decimal places and thousands separator (Mexican format)
  const formatNumber = (num: number): string => {
    // Format with 2 decimal places
    const fixedNum = Number(num.toFixed(2));
    // Convert to string with thousands separator (comma)
    return fixedNum.toLocaleString('es-MX');
  };

  // Función para truncar etiquetas largas
  const truncateLabel = (label: string, maxLength: number = 15): string => {
    if (label.length <= maxLength) return label;
    return label.substring(0, maxLength - 3) + '...';
  };

  return (
    <View style={style}>
      <Svg width={width} height={height + 15}>
        {/* Renderizar barras y etiquetas numéricas */}
        {data.labels.map((label, groupIndex) => {
          const groupX = padding + groupIndex * (barsPerGroup * (barWidth + barSpacing) + groupSpacing);
          return data.datasets.map((ds, barIndex) => {
            const value = ds.data[groupIndex];
            const formattedValue = formatNumber(value);
            const barHeight = maxValue === 0 ? 0 : (value / maxValue) * chartHeight;
            const x = groupX + barIndex * (barWidth + barSpacing);
            const y = topPadding + (height - legendHeight - topPadding) - bottomPadding - barHeight;
            const fill = ds.color ? ds.color() : chartConfig.color(1);
            const labelY = Math.max(topPadding, y - 5);
            return (
              <React.Fragment key={`group-${groupIndex}-bar-${barIndex}`}>
                <Rect x={x} y={y} width={barWidth} height={barHeight} fill={fill} />
                {/* Fondo para la etiqueta - ajustado para móviles */}
                <Rect
                  x={x + barWidth / 2 - 12} // Reducido de 15 a 12
                  y={labelY - 6} // Reducido de 8 a 6
                  width={24} // Reducido de 30 a 24
                  height={10} // Reducido de 12 a 10
                  fill={labelBackgroundFill}
                  rx={3} // Reducido de 4 a 3
                />
                {/* Etiqueta numérica */}
                <Text
                  x={x + barWidth / 2}
                  y={labelY}
                  fontSize="8" // Reducido de 10 a 8 para móviles
                  fill={chartConfig.color(1)}
                  textAnchor="middle"
                  fontWeight="bold"
                >
                  {formattedValue}
                </Text>
              </React.Fragment>
            );
          });
        })}
        {/* Renderizar etiquetas del eje X */}
        {data.labels.map((label, groupIndex) => {
          const groupX = padding + groupIndex * (barsPerGroup * (barWidth + barSpacing) + groupSpacing);
          const groupBlockWidth = barsPerGroup * (barWidth + barSpacing) - barSpacing;
          const centerX = groupX + groupBlockWidth / 2;

          // Mejorar el posicionamiento Y de las etiquetas para evitar superposición
          const labelY = height - legendHeight - (LABEL_SPACING_FROM_BARS - 2); // Usar variable de configuración

          // Truncar etiquetas largas basado en la rotación - ajustado para móviles
          const maxLabelLength = rotation !== 0 ? 15 : 10; // Reducido para móviles
          const displayLabel = truncateLabel(label, maxLabelLength);

          return (
            <Text
              key={`group-label-${groupIndex}`}
              x={centerX}
              y={labelY}
              fontSize="9" // Reducido de 10 a 9 para móviles
              fill={chartConfig.labelColor(1)}
              textAnchor="middle"
              transform={horizontalLabelRotation && horizontalLabelRotation !== 0 ? `rotate(${horizontalLabelRotation}, ${centerX}, ${labelY})` : undefined}
            >
              {displayLabel}
            </Text>
          );
        })}
      </Svg>
      {/* Renderizar la leyenda debajo del gráfico */}
      <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 2, height: legendHeight }}>
        {data.legend.map((metric, idx) => {
          const color = data.datasets[idx] && data.datasets[idx].color ? data.datasets[idx].color() : chartConfig.color(1);
          return (
            <View key={`legend-${idx}`} style={{ flexDirection: 'row', alignItems: 'center', marginHorizontal: 3 }}>
              <View style={{ width: 8, height: 8, backgroundColor: color, marginRight: 2 }} />
              <RNText style={{ fontSize: 9, color: chartConfig.labelColor(1) }}>{metric}</RNText>
            </View>
          );
        })}
      </View>
    </View>
  );
};

export default GroupedBarChart;
