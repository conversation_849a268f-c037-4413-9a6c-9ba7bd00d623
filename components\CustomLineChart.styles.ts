// CustomLineChart.styles.ts
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  container: {
    backgroundColor: "transparent",
  },
  chartContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 8,
    padding: 16,
    marginVertical: 8,
  },
  chartContainerDark: {
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    marginTop: 12,
    paddingHorizontal: 10,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
    marginVertical: 4,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    fontWeight: '500',
  },
  legendTextDark: {
    color: '#fff',
  },
  legendTextLight: {
    color: '#333',
  },
  tooltipContainer: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 8,
    borderRadius: 6,
    zIndex: 1000,
  },
  tooltipContainerDark: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  tooltipText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  tooltipTextDark: {
    color: '#333',
  },
  gridLine: {
    opacity: 0.2,
  },
  dataPoint: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  axisLabel: {
    fontSize: 10,
    fontWeight: '400',
  },
  axisLabelDark: {
    color: '#fff',
  },
  axisLabelLight: {
    color: '#666',
  },
  // Configuración específica para móviles
  mobileOptimized: {
    // Espaciado optimizado para pantallas pequeñas
    paddingHorizontal: 8,
    paddingVertical: 12,
  },
  compactLegend: {
    // Leyenda más compacta para móviles
    marginTop: 8,
    paddingHorizontal: 6,
  },
  compactLegendItem: {
    marginHorizontal: 6,
    marginVertical: 2,
  },
  compactLegendDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 4,
  },
  compactLegendText: {
    fontSize: 11,
    fontWeight: '500',
  },
});

export default styles;
