import { useL<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useRouter } from "expo-router";
import {
  FlatList,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Alert,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Modal,
} from "react-native";
import React, { useState, useEffect, useRef } from "react";
import { MaterialIcons } from "@expo/vector-icons";
import * as Sharing from "expo-sharing";
import * as FileSystem from "expo-file-system";
import * as Clipboard from "expo-clipboard";
import { Audio } from "expo-av";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { DataTable } from "@/components/DataTable";
import { useChat } from "@/context/ChatContext";
import { useAuth } from "@/context/AuthContext";
import { useThemeColor } from "@/hooks/useThemeColor";
import { ENDPOINTS } from "@/constants/Api";
import { useColorScheme } from '@/hooks/useColorScheme';
import { useFavorites } from "@/context/FavoritesContext";

// Tipo de mensaje para la UI
type UIMessage = {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  rawData?: any;
  messageType?: string;
  isTypingIndicator?: boolean;
  isDivider?: boolean;
};

// Función para convertir JSON a CSV
const convertJsonToCsv = (jsonData: any[]): string => {
  if (!Array.isArray(jsonData) || jsonData.length === 0) return "";

  const headers = Object.keys(jsonData[0]);
  const csvRows = [];
  csvRows.push(headers.join(","));
  for (const row of jsonData) {
    const values = headers.map((header) => {
      const value = row[header];
      const escaped =
        typeof value === "string" ? `"${value.replace(/"/g, '""')}"` : value;
      return escaped;
    });
    csvRows.push(values.join(","));
  }
  return csvRows.join("\n");
};

function sameDay(d1: Date, d2: Date) {
  return (
    d1.getFullYear() === d2.getFullYear() &&
    d1.getMonth() === d2.getMonth() &&
    d1.getDate() === d2.getDate()
  );
}

function formatDivider(date: Date): string {
  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);
  const weekdays = [
    "Domingo",
    "Lunes",
    "Martes",
    "Miércoles",
    "Jueves",
    "Viernes",
    "Sábado",
  ];
  const monthNames = [
    "enero",
    "febrero",
    "marzo",
    "abril",
    "mayo",
    "junio",
    "julio",
    "agosto",
    "septiembre",
    "octubre",
    "noviembre",
    "diciembre",
  ];
  if (sameDay(date, today)) return "Hoy";
  if (sameDay(date, yesterday)) return "Ayer";
  const diff = today.getTime() - date.getTime();
  const oneWeek = 7 * 24 * 60 * 60 * 1000;
  if (diff < oneWeek) {
    return weekdays[date.getDay()];
  } else {
    let dayName = weekdays[date.getDay()];
    let day = date.getDate();
    let monthName = monthNames[date.getMonth()];
    let result = `${dayName}, ${day} ${monthName}`;
    if (date.getFullYear() !== today.getFullYear()) {
      result += ` ${date.getFullYear()}`;
    }
    return result;
  }
}

interface DateDividerProps {
  text: string;
}

const DateDivider = ({ text }: DateDividerProps) => {
  const dividerTextColor = useThemeColor(
    {
      light: "rgba(0, 0, 0, 0.45)",
      dark: "rgba(255, 255, 255, 0.45)",
    },
    "text"
  );
  return (
    <View style={styles.dividerContainer}>
      <View style={styles.dividerTextWrapper}>
        <ThemedText style={[styles.dividerText, { color: dividerTextColor }]}>
          {text}
        </ThemedText>
      </View>
    </View>
  );
};

interface MessageOptionsButtonProps {
  message: UIMessage;
  navigateToChart: (data: any) => void;
}

const MessageOptionsButton = ({
  message,
  navigateToChart,
}: MessageOptionsButtonProps) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const handleExportCsv = async () => {
    setMenuVisible(false);
    if (message.rawData && message.messageType === "TablaJSON") {
      const csvContent = convertJsonToCsv(message.rawData);
      const fileUri = `${FileSystem.cacheDirectory}data_export_${Date.now()}.csv`;
      try {
        await FileSystem.writeAsStringAsync(fileUri, csvContent);
        await Sharing.shareAsync(fileUri);
      } catch (error) {
        console.error("Error al exportar CSV:", error);
        Alert.alert("Error", "No se pudo exportar el archivo CSV");
      }
    }
  };

  const handleCopyToClipboard = async () => {
    setMenuVisible(false);
    try {
      if (message.messageType === "TablaJSON" && message.rawData) {
        const csvContent = convertJsonToCsv(message.rawData);
        await Clipboard.setStringAsync(csvContent);
        Alert.alert("Éxito", "CSV copiado al portapapeles");
      } else {
        await Clipboard.setStringAsync(message.text);
        Alert.alert("Éxito", "Texto copiado al portapapeles");
      }
    } catch (error) {
      console.error("Error al copiar al portapapeles:", error);
      Alert.alert("Error", "No se pudo copiar al portapapeles");
    }
  };

  const handleViewChart = () => {
    setMenuVisible(false);
    if (message.messageType === "TablaJSON" && message.rawData) {
      navigateToChart(message.rawData);
    }
  };

  return (
    <View style={styles.optionsButtonContainer}>
      <TouchableOpacity onPress={() => setMenuVisible(!menuVisible)}>
        <MaterialIcons
          name="more-vert"
          size={20}
          color={isDark ? "#fff" : "#666"}
        />
      </TouchableOpacity>
      {menuVisible && (
        <View style={[
          styles.optionsMenu,
          isDark && {
            backgroundColor: "rgba(60, 60, 60, 0.95)",
            borderColor: "rgba(255, 255, 255, 0.1)"
          }
        ]}>
          {message.messageType === "TablaJSON" && (
            <TouchableOpacity
              style={[
                styles.optionItem,
                isDark && { borderBottomColor: "rgba(255, 255, 255, 0.1)" }
              ]}
              onPress={handleExportCsv}
            >
              <MaterialIcons
                name="file-download"
                size={18}
                color={isDark ? "#888" : "#666"}
              />
              <ThemedText style={[
                styles.optionText,
                isDark && { color: "#fff" }
              ]}>Compartir</ThemedText>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={[
              styles.optionItem,
              isDark && { borderBottomColor: "rgba(255, 255, 255, 0.1)" }
            ]}
            onPress={handleCopyToClipboard}
          >
            <MaterialIcons
              name="content-copy"
              size={18}
              color={isDark ? "#888" : "#666"}
            />
            <ThemedText style={[
              styles.optionText,
              isDark && { color: "#fff" }
            ]}>Copiar al portapapeles</ThemedText>
          </TouchableOpacity>
          {message.messageType === "TablaJSON" && (
            <TouchableOpacity
              style={[
                styles.optionItem,
                isDark && { borderBottomColor: "rgba(255, 255, 255, 0.1)" }
              ]}
              onPress={handleViewChart}
            >
              <MaterialIcons
                name="bar-chart"
                size={18}
                color={isDark ? "#888" : "#666"}
              />
              <ThemedText style={[
                styles.optionText,
                isDark && { color: "#fff" }
              ]}>Ver gráfica</ThemedText>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

interface UserMessageOptionsButtonProps {
  message: UIMessage;
  onResend: (text: string) => void;
  onEdit: (text: string) => void;
  botId: number;  // Aseguramos que botId es requerido
}

const UserMessageOptionsButton = ({
  message,
  onResend,
  onEdit,
  botId,  // Recibimos botId como prop
}: UserMessageOptionsButtonProps) => {
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const buttonRef = useRef<View>(null);
  const [menuVisible, setMenuVisible] = useState(false);
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { addFavorite, removeFavorite, isFavorite } = useFavorites();
  const router = useRouter(); // Agregar este hook

  const showMenu = () => {
    buttonRef.current?.measure(
      (x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
        // Position the menu to the left of the button by subtracting the menu width (180px from styles)
        // Add a small gap (8px) from the right edge
        const menuWidth = 180;
        const rightGap = 8;
        setMenuPosition({
          top: pageY + height,
          left: Math.max(rightGap, pageX - menuWidth + width)  // Ensure it doesn't go off the left edge
        });
        setMenuVisible(true);
      }
    );
  };

  const handleCopyToClipboard = async () => {
    setMenuVisible(false);
    try {
      await Clipboard.setStringAsync(message.text);
      Alert.alert("Éxito", "Texto copiado al portapapeles");
    } catch (error) {
      console.error("Error al copiar al portapapeles:", error);
      Alert.alert("Error", "No se pudo copiar al portapapeles");
    }
  };

  const handleResend = () => {
    setMenuVisible(false);
    onResend(message.text);
  };

  const handleEdit = () => {
    setMenuVisible(false);
    onEdit(message.text);
  };

  const handleFavorite = () => {
    setMenuVisible(false);
    if (isFavorite(message.id)) {
      removeFavorite(message.id);
    } else {
      addFavorite({
        id: message.id,
        text: message.text,
        timestamp: message.timestamp,
        botId: botId,  // Usamos el botId que recibimos como prop
      });
    }
  };

  const handleSchedule = () => {
    setMenuVisible(false);
    router.push({
      pathname: '/scheduler',
      params: {
        messageId: message.id,
        messageText: message.text,
        botId: botId.toString()
      }
    });
  };

  return (
    <>
      <View style={styles.optionsButtonContainer}>
        <TouchableOpacity
          ref={buttonRef}
          onPress={showMenu}
        >
          <MaterialIcons
            name="more-vert"
            size={20}
            color="#fff"
          />
        </TouchableOpacity>
      </View>
      <Modal
        visible={menuVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setMenuVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setMenuVisible(false)}
        >
          <View
            style={[
              styles.optionsMenu,
              {
                position: 'absolute',
                top: menuPosition.top,
                left: menuPosition.left,
              },
              isDark && {
                backgroundColor: "rgba(60, 60, 60, 0.95)",
                borderColor: "rgba(255, 255, 255, 0.1)"
              }
            ]}
          >
            <TouchableOpacity
              style={[styles.optionItem, isDark && { borderBottomColor: "rgba(255, 255, 255, 0.1)" }]}
              onPress={handleResend}
            >
              <MaterialIcons name="send" size={18} color={isDark ? "#888" : "#666"} />
              <ThemedText style={[styles.optionText, isDark && { color: "#fff" }]}>
                Reenviar
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.optionItem, isDark && { borderBottomColor: "rgba(255, 255, 255, 0.1)" }]}
              onPress={handleEdit}
            >
              <MaterialIcons name="edit" size={18} color={isDark ? "#888" : "#666"} />
              <ThemedText style={[styles.optionText, isDark && { color: "#fff" }]}>
                Editar
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.optionItem, isDark && { borderBottomColor: "rgba(255, 255, 255, 0.1)" }]}
              onPress={handleCopyToClipboard}
            >
              <MaterialIcons name="content-copy" size={18} color={isDark ? "#888" : "#666"} />
              <ThemedText style={[styles.optionText, isDark && { color: "#fff" }]}>
                Copiar
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.optionItem, isDark && { borderBottomColor: "rgba(255, 255, 255, 0.1)" }]}
              onPress={handleFavorite}
            >
              <MaterialIcons
                name={isFavorite(message.id) ? "star" : "star-border"}
                size={18}
                color={isDark ? "#888" : "#666"}
              />
              <ThemedText style={[styles.optionText, isDark && { color: "#fff" }]}>
                {isFavorite(message.id) ? "Quitar de Favoritos" : "Agregar a Favoritos"}
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.optionItem, isDark && { borderBottomColor: "rgba(255, 255, 255, 0.1)" }]}
              onPress={handleSchedule}
            >
              <MaterialIcons
                name="schedule"
                size={18}
                color={isDark ? "#888" : "#666"}
              />
              <ThemedText style={[styles.optionText, isDark && { color: "#fff" }]}>
                Programar
              </ThemedText>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default function ChatScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const botId = parseInt(id as string, 10);
  const [message, setMessage] = useState("");
  const [uiMessages, setUIMessages] = useState<UIMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);

  // Estados para grabación de audio con expo-av
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioUri, setAudioUri] = useState<string | null>(null);
  const [isTranscribing, setIsTranscribing] = useState(false);

  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Colores temáticos
  const textColor = useThemeColor({}, "text");
  const inputBackgroundColor = useThemeColor(
    { light: "rgba(0, 0, 0, 0.05)", dark: "rgba(255, 255, 255, 0.1)" },
    "background"
  );
  const dividerTextColor = useThemeColor(
    { light: "#000", dark: "#fff" },
    "text"
  );
  const botMessageBackground = useThemeColor(
    { light: "rgba(0, 0, 0, 0.05)", dark: "rgba(255, 255, 255, 0.1)" },
    "background"
  );

  const {
    histories,
    loadingHistories,
    fetchHistoryForBot,
    setCurrentBot,
    sendMessageToBot,
    sendingMessage,
  } = useChat();
  const { authData } = useAuth();

  // Función para grabar audio usando expo-av
  const startAudioRecording = async () => {
    try {
      console.log("Solicitando permisos para grabar audio...");
      const permission = await Audio.requestPermissionsAsync();
      if (permission.status !== "granted") {
        alert("Se requieren permisos para grabar audio.");
        return;
      }
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });
      console.log("Iniciando grabación…");
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      setRecording(recording);
      setIsRecording(true);
    } catch (err) {
      console.error("Error al iniciar la grabación:", err);
    }
  };

  const stopAudioRecording = async () => {
    if (!recording) return;
    try {
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      setAudioUri(uri);
      setRecording(null);
      setIsRecording(false);
      console.log("Grabación detenida. Archivo:", uri);
      if (uri) {
        if (!authData || !authData.access_token) {
          console.error("No hay token de acceso disponible.");
          return;
        }
        await transcribeAudio(uri, authData.access_token);

      }
    } catch (error) {
      console.error("Error al detener la grabación:", error);
    }
  };

  // Función para enviar el audio a tu endpoint /transcript y obtener la transcripción
  const transcribeAudio = async (uri: string, accessToken: string) => {
    // Activar estado de transcripción
    setIsTranscribing(true);

    // Preparamos FormData con el archivo de audio usando la key "audio_file"
    const formData = new FormData();
    formData.append("audio_file", {
      uri: uri,
      name: "audio.m4a", // Ajusta el nombre y extensión según corresponda
      type: "audio/m4a",
    } as any);

    try {
      const response = await fetch(ENDPOINTS.TRANSCRIPT, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          // No es necesario establecer "Content-Type" al enviar FormData
        },
        body: formData,
      });

      const json = await response.json();
      console.log("Transcripción recibida:", json);

      if (json.text) {
        setMessage(json.text);
      } else {
        console.error("Error en la transcripción:", json);

        // Manejar errores específicos del servicio de transcripción
        if (json.detail) {
          // Mostrar mensaje amigable al usuario
          Alert.alert(
            "Servicio no disponible",
            "El servicio de voz a texto no está disponible en este momento. Por favor, intenta escribir tu mensaje o inténtalo más tarde.",
            [{ text: "Entendido" }]
          );
        } else {
          // Error genérico de transcripción
          Alert.alert(
            "Error de transcripción",
            "No se pudo procesar el audio. Por favor, intenta grabar nuevamente.",
            [{ text: "Entendido" }]
          );
        }
      }
    } catch (error) {
      console.error("Error al enviar audio a la transcripción:", error);

      // Error de conexión o red
      Alert.alert(
        "Error de conexión",
        "No se pudo conectar con el servicio de voz a texto. Verifica tu conexión a internet e intenta nuevamente.",
        [{ text: "Entendido" }]
      );
    } finally {
      // Desactivar estado de transcripción independientemente del resultado
      setIsTranscribing(false);
    }
  };


  // Función para navegar a la pantalla de gráfica
  const navigateToChart = (data: any) => {
    router.push({
      pathname: "/chart",
      params: { data: JSON.stringify(data) },
    });
  };

  // Inicialización del chat
  useEffect(() => {
    const initChat = async () => {
      setLoading(true);
      setCurrentBot(botId);
      if (!histories[botId] && !loadingHistories[botId]) {
        try {
          await fetchHistoryForBot(botId);
        } catch (error) {
          console.error("Failed to fetch chat history:", error);
        }
      }
      setLoading(false);
    };
    initChat();
  }, [botId]);

  // Procesar el historial de chat en mensajes para la UI
  useEffect(() => {
    if (histories[botId] && authData) {
      console.log(
        `Processing ${histories[botId].length} messages for bot ${botId}`
      );
      const convertedMessages: any[] = [];
      const processedMessageIds = new Set<string>();
      histories[botId].forEach((chatMessage, index) => {
        const messageUniqueId = `${chatMessage.RID}-${index}`;
        if (processedMessageIds.has(messageUniqueId)) return;
        if (chatMessage.Mensaje && chatMessage.Mensaje.trim() !== "") {
          const userMessage = {
            id: `user-${messageUniqueId}`,
            text: chatMessage.Mensaje || "Empty message",
            isBot: false,
            timestamp: new Date(chatMessage.FechaRegistro),
            messageType: chatMessage.MensajeTipo,
          };
          convertedMessages.push(userMessage);
          processedMessageIds.add(messageUniqueId);
        }
        if (
          chatMessage.Respuesta !== undefined &&
          chatMessage.Respuesta !== null
        ) {
          let botText = "";
          let botRawData = undefined;
          if (typeof chatMessage.Respuesta === "string") {
            botText = chatMessage.Respuesta;
          } else if (chatMessage.MensajeTipo === "TablaJSON") {
            const tableData = chatMessage.Respuesta;
            if (Array.isArray(tableData) && tableData.length > 0) {
              botText = `${tableData.length} registros encontrados.`;
              botRawData = tableData;
            } else {
              botText = "No se encontraron datos para esta consulta.";
            }
          } else {
            try {
              botText = JSON.stringify(chatMessage.Respuesta, null, 2);
            } catch (e) {
              botText = "Complex data (cannot display)";
            }
            botRawData = chatMessage.Respuesta;
          }
          if (botText !== "Sin respuesta" && botText.trim() !== "") {
            const botMessage = {
              id: `bot-${messageUniqueId}`,
              text: botText,
              isBot: true,
              timestamp: new Date(chatMessage.FechaRegistro),
              rawData: botRawData,
              messageType: chatMessage.MensajeTipo,
            };
            convertedMessages.push(botMessage);
          }
        }
      });
      console.log(
        `Converted ${histories[botId].length} API messages into ${convertedMessages.length} UI messages`
      );
      convertedMessages.sort(
        (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
      );
      const groupedMessages: any[] = [];
      let lastDate: Date | null = null;
      convertedMessages.forEach((msg) => {
        const currentDate = msg.timestamp;
        if (!lastDate || !sameDay(currentDate, lastDate)) {
          groupedMessages.push({
            id: `divider-${currentDate.getTime()}`,
            text: formatDivider(currentDate),
            isDivider: true,
            timestamp: currentDate,
          });
        }
        groupedMessages.push(msg);
        lastDate = currentDate;
      });
      const typingIndicator = uiMessages.find((msg) => msg.isTypingIndicator);
      setUIMessages(
        typingIndicator
          ? [...groupedMessages, typingIndicator]
          : groupedMessages
      );
    }
  }, [histories[botId], authData]);

  // Función para desplazar la FlatList hasta el final
  const scrollToBottom = () => {
    if (flatListRef.current && uiMessages.length > 0) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  };

  useEffect(() => {
    if (uiMessages.length > 0) {
      setTimeout(scrollToBottom, 100);
    }
  }, [uiMessages]);

  const handleSend = async () => {
    if (!message.trim() || sendingMessage) return;
    const messageText = message.trim();
    setMessage("");
    const typingIndicatorId = `typing-${Date.now()}`;
    setIsWaitingForResponse(true);
    setUIMessages((prev) => [
      ...prev,
      {
        id: typingIndicatorId,
        text: "Escribiendo...",
        isBot: true,
        timestamp: new Date(),
        isTypingIndicator: true,
      } as UIMessage,
    ]);
    try {
      console.log(`Sending message to bot ${botId}: ${messageText}`);
      const response = await sendMessageToBot(botId, messageText);
      setUIMessages((prev) =>
        prev.filter((msg) => msg.id !== typingIndicatorId)
      );
      setIsWaitingForResponse(false);
      if (!response) {
        console.error("No response received from bot");
        const errorMessage: UIMessage = {
          id: `error-${Date.now()}`,
          text: "No se recibió respuesta. Por favor, intenta de nuevo.",
          isBot: true,
          timestamp: new Date(),
        };
        setUIMessages((prev) => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error("Error sending message:", error);
      setUIMessages((prev) =>
        prev.filter((msg) => msg.id !== typingIndicatorId)
      );
      setIsWaitingForResponse(false);
      const errorMessage: UIMessage = {
        id: `error-${Date.now()}`,
        text: "Error al enviar el mensaje. Por favor, intenta de nuevo.",
        isBot: true,
        timestamp: new Date(),
      };
      setUIMessages((prev) => [...prev, errorMessage]);
    }
  };

  const handleResend = async (text: string) => {
    if (!text.trim()) return;

    const typingIndicatorId = `typing-${Date.now()}`;
    setIsWaitingForResponse(true);
    setUIMessages((prev) => [
      ...prev,
      {
        id: typingIndicatorId,
        text: "Escribiendo...",
        isBot: true,
        timestamp: new Date(),
        isTypingIndicator: true,
      } as UIMessage,
    ]);

    try {
      console.log(`Reenviando mensaje al bot ${botId}: ${text}`);
      const response = await sendMessageToBot(botId, text);

      setUIMessages((prev) =>
        prev.filter((msg) => msg.id !== typingIndicatorId)
      );
      setIsWaitingForResponse(false);

      if (!response) {
        console.error("No se recibió respuesta del bot");
        const errorMessage: UIMessage = {
          id: `error-${Date.now()}`,
          text: "No se recibió respuesta. Por favor, intenta de nuevo.",
          isBot: true,
          timestamp: new Date(),
        };
        setUIMessages((prev) => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error("Error al reenviar mensaje:", error);
      setUIMessages((prev) =>
        prev.filter((msg) => msg.id !== typingIndicatorId)
      );
      setIsWaitingForResponse(false);
      const errorMessage: UIMessage = {
        id: `error-${Date.now()}`,
        text: "Error al enviar el mensaje. Por favor, intenta de nuevo.",
        isBot: true,
        timestamp: new Date(),
      };
      setUIMessages((prev) => [...prev, errorMessage]);
    }
  };

  const handleEdit = (text: string) => {
    setMessage(text);
  };

  const renderMessageContent = (message: any) => {
    if (message.isDivider) return <DateDivider text={message.text} />;
    if (message.isTypingIndicator) {
      return (
        <View style={styles.typingIndicator}>
          <ActivityIndicator size="small" color="#fff" />
          <ThemedText style={styles.typingText}>Escribiendo...</ThemedText>
        </View>
      );
    }
    if (message.messageType === "TablaJSON" && message.rawData) {
      return (
        <View>
          <ThemedText>{message.text}</ThemedText>
          <View style={styles.tableContainer}>
            <ThemedText style={styles.tableTitle}>Data Table</ThemedText>
            <DataTable data={message.rawData} />
          </View>
        </View>
      );
    }
    if (
      message.isBot &&
      message.rawData &&
      message.messageType !== "TablaJSON"
    ) {
      return (
        <View>
          <ThemedText>{message.text}</ThemedText>
          <ThemedText style={styles.dataIndicator}>
            [Contains additional data]
          </ThemedText>
        </View>
      );
    }
    if (
      message.text &&
      message.text.trim() !== "" &&
      message.text !== "Sin respuesta"
    ) {
      return <ThemedText>{message.text}</ThemedText>;
    }
    return <ThemedText>Mensaje sin contenido</ThemedText>;
  };

  if (loading || loadingHistories[botId]) {
    return (
      <ThemedView style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" />
        <ThemedText style={styles.loadingText}>
          Cargando historial de chat...
        </ThemedText>
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
    >
      <ThemedView style={styles.container}>
        {uiMessages.length === 0 ? (
          <ThemedView style={styles.emptyContainer}>
            <ThemedText>
              No hay mensajes aún. ¡Inicia una conversación!
            </ThemedText>
          </ThemedView>
        ) : (
          <FlatList
            ref={flatListRef}
            data={uiMessages}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => {
              if (item.isDivider) return <DateDivider text={item.text} />;
              return (
                <View
                  style={[
                    styles.messageContainer,
                    item.isBot ? [styles.botMessage, { backgroundColor: botMessageBackground }] : styles.userMessage,
                    item.messageType === "TablaJSON" && [
                      styles.tableMessageContainer,

                    ],
                  ]}
                >
                  {item.isBot && !item.isTypingIndicator ? (
                    <MessageOptionsButton
                      message={item}
                      navigateToChart={navigateToChart}
                    />
                  ) : !item.isBot && (
                    <UserMessageOptionsButton
                      message={item}
                      onResend={handleResend}
                      onEdit={handleEdit}
                      botId={botId}  // Pasamos el botId como prop
                    />
                  )}
                  {renderMessageContent(item)}
                  <ThemedText style={styles.timestampText}>
                    {item.timestamp.toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </ThemedText>
                </View>
              );
            }}
            contentContainerStyle={styles.messagesList}
            onScroll={(event: NativeSyntheticEvent<NativeScrollEvent>) => {
              const offsetY = event.nativeEvent.contentOffset.y;
              const contentHeight = event.nativeEvent.contentSize.height;
              const scrollViewHeight = event.nativeEvent.layoutMeasurement.height;

              // Si el usuario no está cerca del fondo (con un margen de 100px)
              const isNotAtBottom = offsetY < contentHeight - scrollViewHeight - 100;
              setShowScrollButton(isNotAtBottom);
            }}
            scrollEventThrottle={16}
            showsVerticalScrollIndicator={true}
            initialNumToRender={10}
            maxToRenderPerBatch={5}
            windowSize={10}
          />
        )}
        {showScrollButton && (
          <View style={styles.scrollButtonContainer}>
            <TouchableOpacity
              style={[
                styles.scrollButton,
                isDark && { backgroundColor: 'rgba(60, 60, 60, 0.9)' }
              ]}
              onPress={scrollToBottom}
            >
              <MaterialIcons
                name="arrow-downward"
                size={24}
                color={isDark ? "#fff" : "#0a7ea4"}
              />
            </TouchableOpacity>
          </View>
        )}
        {/* Indicador de transcripción */}
        {isTranscribing && (
          <View style={styles.transcribingIndicator}>
            <ThemedText style={styles.transcribingText}>
              🎤 Procesando audio...
            </ThemedText>
          </View>
        )}
        <View style={styles.inputContainer}>
          <TextInput
            value={message}
            onChangeText={setMessage}
            placeholder="Escribe un mensaje..."
            placeholderTextColor="#999"
            style={[
              styles.input,
              { color: textColor, backgroundColor: inputBackgroundColor },
            ]}
            editable={!sendingMessage}
            multiline
            textAlignVertical="top"
            returnKeyType="default"
            blurOnSubmit={false}
          />
          {/* Botón de grabación: si se está grabando, detiene la grabación y transcribe; si no, inicia la grabación */}
          <TouchableOpacity
            onPress={() => {
              if (isRecording) {
                stopAudioRecording();
              } else {
                startAudioRecording();
              }
            }}
            style={[
              styles.micButton,
              isTranscribing && styles.micButtonTranscribing
            ]}
            disabled={isTranscribing}
          >
            {isTranscribing ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <MaterialIcons
                name={isRecording ? "mic-off" : "mic"}
                size={24}
                color="#fff"
              />
            )}
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleSend}
            style={[
              styles.sendButton,
              sendingMessage && styles.sendButtonDisabled,
              { alignSelf: "flex-end" },
            ]}
            disabled={sendingMessage || !message.trim()}
          >
            {sendingMessage ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <ThemedText style={styles.sendButtonText}>Enviar</ThemedText>
            )}
          </TouchableOpacity>
        </View>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative', // Asegura que los elementos absolutos se posicionen correctamente
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  messagesList: {
    padding: 16,
    gap: 8,
  },
  messageContainer: {
    position: "relative",
    padding: 12,
    borderRadius: 16,
    maxWidth: "80%",
    marginVertical: 4,
  },
  tableMessageContainer: {
    maxWidth: "95%",
    width: "95%",
  },
  botMessage: {
    alignSelf: "flex-start",
    borderBottomLeftRadius: 4,
  },
  userMessage: {
    alignSelf: "flex-end",
    backgroundColor: "#0a7ea4",
    borderBottomRightRadius: 4,
    overflow: "visible",
  },
  messageText: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  timestampText: {
    fontSize: 10,
    opacity: 0.7,
    marginTop: 4,
    alignSelf: "flex-end",
  },
  dataIndicator: {
    fontSize: 12,
    marginTop: 4,
    opacity: 0.7,
  },
  tableContainer: {
    width: "100%",
    marginVertical: 4,
  },
  tableTitle: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
  },
  inputContainer: {
    flexDirection: "row",
    padding: 16,
    gap: 8,
    borderTopWidth: 1,
    borderTopColor: "rgba(255, 255, 255, 0.1)",
    alignItems: "flex-end",
  },
  input: {
    flex: 1,
    minHeight: 40,
    maxHeight: 100,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  sendButton: {
    paddingHorizontal: 16,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#0a7ea4",
    borderRadius: 20,
  },
  sendButtonDisabled: {
    backgroundColor: "#0a7ea480",
  },
  sendButtonText: {
    color: "#fff",
    fontWeight: "600",
  },
  micButton: {
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#0a7ea4",
    borderRadius: 20,
    width: 40,
    height: 40,
  },
  micButtonTranscribing: {
    backgroundColor: "#ff9500", // Color naranja para indicar procesamiento
    opacity: 0.8,
  },
  transcribingIndicator: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: "center",
  },
  transcribingText: {
    fontSize: 12,
    opacity: 0.8,
    fontStyle: "italic",
  },
  typingIndicator: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  typingText: {
    fontSize: 14,
    opacity: 0.8,
  },
  dividerContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginVertical: 12,
    width: "100%",
  },
  dividerTextWrapper: {
    backgroundColor: "rgba(225, 245, 254, 0.13)",
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
  },
  dividerText: {
    fontSize: 12,
    fontWeight: "500",
    textAlign: "center",
  },
  optionsButtonContainer: {
    position: "absolute",
    top: 5,
    right: 5,
    zIndex: 100,
  },
  optionsMenu: {
    position: "absolute",
    top: 24,
    right: 0,
    backgroundColor: "rgba(255, 255, 255, 0.95)", // Light mode background
    borderRadius: 8,
    padding: 4,
    minWidth: 180,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 101,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)", // Subtle border
  },
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  optionText: {
    marginLeft: 8,
    fontSize: 14,
    color: "#333", // Darker text for better contrast in light mode
  },
  scrollButtonContainer: {
    position: 'absolute',
    right: 16,
    bottom: 80,
    zIndex: 1000,
  },
  scrollButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
});
