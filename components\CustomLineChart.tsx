import React from 'react';
import { View, Text as RNText } from 'react-native';
import { Svg, Line, Circle, Text, Polyline, Path } from 'react-native-svg';

/**
 * CONFIGURACIÓN RÁPIDA:
 * Para ajustar el espacio entre las líneas y las etiquetas del eje X,
 * modifica las variables de configuración dentro del componente.
 * - LABEL_SPACING_FROM_CHART: Espacio entre el gráfico y las etiquetas (valores recomendados: 15-30)
 * - POINT_RADIUS: Radio de los puntos en las líneas (valores recomendados: 3-6)
 * - GRID_LINE_OPACITY: Opacidad de las líneas de la cuadrícula (valores recomendados: 0.1-0.3)
 * - BEZIER_CURVE_INTENSITY: Intensidad de las curvas Bézier (valores recomendados: 0.1-0.5)
 * - BEZIER_SLOPE_INFLUENCE: Influencia de la pendiente en las curvas (valores recomendados: 0.1-0.3)
 *
 * CARACTERÍSTICAS:
 * - Curvas Bézier suaves y configurables
 * - Etiquetas con truncamiento automático (ellipsis)
 * - Optimizado para dispositivos móviles
 * - Soporte para múltiples datasets
 * - Interacción táctil en puntos de datos
 */

interface Dataset {
  data: number[];
  color?: () => string;
  strokeWidth?: number;
  label?: string;
}

interface CustomLineChartData {
  labels: string[];
  datasets: Dataset[];
  legend?: string[];
}

interface CustomLineChartProps {
  data: CustomLineChartData;
  width: number;
  height: number;
  chartConfig: {
    backgroundColor: string;
    backgroundGradientFrom: string;
    backgroundGradientTo: string;
    decimalPlaces: number;
    color: (opacity?: number) => string;
    labelColor: (opacity?: number) => string;
  };
  style?: any;
  horizontalLabelRotation?: number;
  showDots?: boolean;
  showGrid?: boolean;
  bezier?: boolean;
  onDataPointClick?: (data: { value: number; index: number; dataset: number }) => void;
}

const CustomLineChart: React.FC<CustomLineChartProps> = ({
  data,
  width,
  height,
  chartConfig,
  style,
  horizontalLabelRotation = 0,
  showDots = true,
  showGrid = true,
  bezier = false,
  onDataPointClick
}) => {
  // *** CONFIGURACIÓN RÁPIDA ***
  const LABEL_SPACING_FROM_CHART = 100; // Espacio entre el gráfico y las etiquetas
  const POINT_RADIUS = 4; // Radio de los puntos
  const GRID_LINE_OPACITY = 0.2; // Opacidad de las líneas de la cuadrícula
  const LABEL_FONT_SIZE = 11; // Tamaño de fuente para etiquetas móviles
  const BEZIER_CURVE_INTENSITY = 0.3; // Intensidad de las curvas Bézier (0.1-0.5 recomendado)
  const BEZIER_SLOPE_INFLUENCE = 0.2; // Influencia de la pendiente en las curvas (0.1-0.3 recomendado)

  const leftPadding = 50; // Más espacio para números del eje Y
  const rightPadding = 20;
  const legendHeight = data.legend && data.legend.length > 0 ? 40 : 0;
  const bottomPadding = 80; // Más espacio para etiquetas del eje X
  const topPadding = 20;

  // Calcular dimensiones del área del gráfico
  const chartHeight = height - legendHeight - topPadding - bottomPadding;
  const chartWidth = width - leftPadding - rightPadding;

  // Función para formatear números
  const formatNumber = (value: number): string => {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M';
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'K';
    }
    return value.toFixed(chartConfig.decimalPlaces || 0);
  };

  // Calcular valores máximo y mínimo
  const allValues = data.datasets.flatMap(dataset => dataset.data);
  const maxValue = Math.max(...allValues);
  const minValue = Math.min(...allValues, 0);
  const valueRange = maxValue - minValue || 1;

  // Función para obtener coordenadas Y
  const getYCoordinate = (value: number): number => {
    const normalizedValue = (value - minValue) / valueRange;
    return topPadding + chartHeight - (normalizedValue * chartHeight);
  };

  // Función para obtener coordenadas X
  const getXCoordinate = (index: number): number => {
    const stepWidth = chartWidth / (data.labels.length - 1 || 1);
    return leftPadding + (index * stepWidth);
  };

  // Generar puntos para las líneas
  const generateLinePoints = (dataset: Dataset): string => {
    return dataset.data
      .map((value, index) => {
        const x = getXCoordinate(index);
        const y = getYCoordinate(value);
        return `${x},${y}`;
      })
      .join(' ');
  };

  // Generar path SVG para curvas Bézier suaves
  const generateBezierPath = (dataset: Dataset): string => {
    if (dataset.data.length < 2) return '';

    const points = dataset.data.map((value, index) => ({
      x: getXCoordinate(index),
      y: getYCoordinate(value)
    }));

    let path = `M ${points[0].x} ${points[0].y}`;

    for (let i = 1; i < points.length; i++) {
      const prevPoint = points[i - 1];
      const currentPoint = points[i];

      // Calcular puntos de control para curvas más suaves usando configuración
      const controlPointDistance = Math.abs(currentPoint.x - prevPoint.x) * BEZIER_CURVE_INTENSITY;

      // Ajustar los puntos de control considerando la pendiente
      let cp1x = prevPoint.x + controlPointDistance;
      let cp1y = prevPoint.y;
      let cp2x = currentPoint.x - controlPointDistance;
      let cp2y = currentPoint.y;

      // Si hay un punto anterior y siguiente, suavizar la curva
      if (i > 1 && i < points.length - 1) {
        const prevPrevPoint = points[i - 2];
        const nextPoint = points[i + 1];

        // Calcular la pendiente promedio para suavizar
        const prevSlope = (prevPoint.y - prevPrevPoint.y) / (prevPoint.x - prevPrevPoint.x);
        const nextSlope = (nextPoint.y - currentPoint.y) / (nextPoint.x - currentPoint.x);

        // Ajustar los puntos de control basándose en las pendientes usando configuración
        cp1y = prevPoint.y + (prevSlope * controlPointDistance * BEZIER_SLOPE_INFLUENCE);
        cp2y = currentPoint.y - (nextSlope * controlPointDistance * BEZIER_SLOPE_INFLUENCE);
      }

      path += ` C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${currentPoint.x} ${currentPoint.y}`;
    }

    return path;
  };

  // Función para truncar etiquetas largas (igual que en GroupedBarChart)
  const truncateLabel = (label: string, maxLength: number = 10): string => {
    if (label.length <= maxLength) return label;
    return label.substring(0, maxLength - 3) + '...';
  };

  // Función para manejar click en punto de datos
  const handleDataPointClick = (value: number, index: number, datasetIndex: number) => {
    if (onDataPointClick) {
      onDataPointClick({ value, index, dataset: datasetIndex });
    }
  };

  return (
    <View style={style}>
      <Svg width={width} height={height}>
        {/* Líneas de la cuadrícula */}
        {showGrid && (
          <>
            {/* Líneas horizontales */}
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
              const y = topPadding + chartHeight * ratio;
              return (
                <Line
                  key={`grid-h-${index}`}
                  x1={leftPadding}
                  y1={y}
                  x2={leftPadding + chartWidth}
                  y2={y}
                  stroke={chartConfig.color(GRID_LINE_OPACITY)}
                  strokeWidth={1}
                />
              );
            })}

            {/* Líneas verticales */}
            {data.labels.map((_, index) => {
              const x = getXCoordinate(index);
              return (
                <Line
                  key={`grid-v-${index}`}
                  x1={x}
                  y1={topPadding}
                  x2={x}
                  y2={topPadding + chartHeight}
                  stroke={chartConfig.color(GRID_LINE_OPACITY)}
                  strokeWidth={1}
                />
              );
            })}
          </>
        )}

        {/* Renderizar líneas de datos */}
        {data.datasets.map((dataset, datasetIndex) => {
          const lineColor = dataset.color ? dataset.color() : chartConfig.color(1);
          const strokeWidth = dataset.strokeWidth || 2;

          return (
            <React.Fragment key={`dataset-${datasetIndex}`}>
              {/* Línea con curvas Bézier o línea recta */}
              {bezier ? (
                <Path
                  d={generateBezierPath(dataset)}
                  fill="none"
                  stroke={lineColor}
                  strokeWidth={strokeWidth}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              ) : (
                <Polyline
                  points={generateLinePoints(dataset)}
                  fill="none"
                  stroke={lineColor}
                  strokeWidth={strokeWidth}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              )}

              {/* Puntos de datos */}
              {showDots && dataset.data.map((value, pointIndex) => {
                const x = getXCoordinate(pointIndex);
                const y = getYCoordinate(value);

                return (
                  <React.Fragment key={`point-${datasetIndex}-${pointIndex}`}>
                    {/* Área de toque invisible más grande */}
                    <Circle
                      cx={x}
                      cy={y}
                      r={POINT_RADIUS * 2}
                      fill="transparent"
                      onPress={() => handleDataPointClick(value, pointIndex, datasetIndex)}
                    />

                    {/* Punto visible */}
                    <Circle
                      cx={x}
                      cy={y}
                      r={POINT_RADIUS}
                      fill={lineColor}
                      stroke="#fff"
                      strokeWidth={2}
                    />
                  </React.Fragment>
                );
              })}
            </React.Fragment>
          );
        })}

        {/* Etiquetas del eje Y */}
        {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
          const value = minValue + (valueRange * (1 - ratio));
          const y = topPadding + chartHeight * ratio;

          return (
            <Text
              key={`y-label-${index}`}
              x={leftPadding - 8}
              y={y + 4}
              fontSize={LABEL_FONT_SIZE}
              fill={chartConfig.labelColor(1)}
              textAnchor="end"
            >
              {formatNumber(value)}
            </Text>
          );
        })}

        {/* Etiquetas del eje X (igual que en GroupedBarChart) */}
        {data.labels.map((label, index) => {
          const x = getXCoordinate(index);
          const y = topPadding + chartHeight + LABEL_SPACING_FROM_CHART;

          // Truncar etiquetas largas basado en la rotación (igual que en GroupedBarChart)
          const maxLabelLength = horizontalLabelRotation !== 0 ? 15 : 10;
          const displayLabel = truncateLabel(label, maxLabelLength);

          return (
            <Text
              key={`x-label-${index}`}
              x={x}
              y={y + 4}
              fontSize={LABEL_FONT_SIZE}
              fill={chartConfig.labelColor(1)}
              textAnchor="middle"
              transform={horizontalLabelRotation ? `rotate(${horizontalLabelRotation} ${x} ${y + 4})` : undefined}
            >
              {displayLabel}
            </Text>
          );
        })}
      </Svg>

      {/* Leyenda */}
      {data.legend && data.legend.length > 0 && (
        <View style={{
          flexDirection: 'row',
          justifyContent: 'center',
          flexWrap: 'wrap',
          marginTop: 10,
          paddingHorizontal: 10
        }}>
          {data.legend.map((legendItem, index) => {
            const color = data.datasets[index]?.color ? data.datasets[index].color!() : chartConfig.color(1);
            return (
              <View
                key={`legend-${index}`}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginHorizontal: 8,
                  marginVertical: 2
                }}
              >
                <View
                  style={{
                    width: 12,
                    height: 12,
                    backgroundColor: color,
                    borderRadius: 6,
                    marginRight: 6
                  }}
                />
                <RNText
                  style={{
                    fontSize: 12,
                    color: chartConfig.labelColor(1)
                  }}
                >
                  {legendItem}
                </RNText>
              </View>
            );
          })}
        </View>
      )}
    </View>
  );
};

export default CustomLineChart;
